{"name": "cost-estimation-workspace", "version": "1.0.0", "private": true, "type": "module", "workspaces": ["packages/*"], "scripts": {"dev": "vite", "build": "npm run build:packages && vite build", "build:packages": "npm run build:rough-estimate && npm run build:main-shell && npm run build:shared-components", "build:rough-estimate": "cd packages/rough-estimate && npm run build && npm run copy-dist", "build:main-shell": "cd packages/main-shell && npm run build && npm run copy-dist", "build:shared-components": "cd packages/shared-components && npm run build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "dev:all": "node dev-setup.js", "dev:shared": "cd packages/shared-components && npm run dev", "dev:shared-watch": "cd packages/shared-components && npm run build:watch"}, "dependencies": {"@tauri-apps/api": "^2.5.0", "ant-design-vue": "^4.2.6", "pinia": "^3.0.3", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@tauri-apps/cli": "^2.5.0", "@vitejs/plugin-vue": "^5.2.3", "less": "^4.4.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.2.4"}}