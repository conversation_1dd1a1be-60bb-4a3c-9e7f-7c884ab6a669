# 测试共享头部组件

## 已完成的修改

### 1. 概算模块 (rough-estimate)
- ✅ 替换原生头部为 `SimpleHeader` 组件
- ✅ 添加窗口控制方法 (`onMinimize`, `onMaximize`, `onClose`)
- ✅ 移除原有头部样式
- ✅ 保留操作按钮在右侧插槽中

### 2. 主应用模块 (main-shell)
- ✅ 替换原生头部为 `AppHeader` 组件
- ✅ 将导航菜单移至中间插槽
- ✅ 添加窗口控制方法
- ✅ 更新样式以适配新的头部组件

### 3. Tauri 窗口配置
- ✅ 子窗口设置 `decorations(false)` - 不显示原生标题栏
- ✅ 模块窗口设置 `decorations(false)` - 不显示原生标题栏
- ✅ 保持主窗口 `decorations(false)` - 使用自定义头部

## 测试步骤

1. **启动开发环境**
   ```bash
   cd demo
   npm run dev:all
   ```

2. **测试主应用**
   - 主应用应显示自定义头部组件
   - 头部包含标题、导航菜单和窗口控制按钮
   - 窗口控制按钮应正常工作

3. **测试概算模块**
   - 点击概算模块卡片
   - 新窗口应显示 SimpleHeader 组件
   - 头部包含标题、操作按钮和窗口控制按钮
   - 不应显示原生标题栏

4. **测试窗口控制功能**
   - 最小化按钮：窗口应最小化
   - 最大化按钮：窗口应最大化/还原
   - 关闭按钮：窗口应关闭

## 预期效果

- ✅ 所有子窗口都使用自定义头部组件
- ✅ 不显示原生标题栏
- ✅ 窗口控制功能正常工作
- ✅ 头部样式统一美观
- ✅ 支持拖拽移动窗口

## 如果遇到问题

1. **头部组件不显示**
   - 检查 shared-components 是否正确构建
   - 检查导入路径是否正确

2. **仍显示原生标题栏**
   - 检查 Tauri 配置中的 `decorations` 设置
   - 重新构建应用

3. **窗口控制不工作**
   - 检查 Tauri API 导入
   - 检查窗口管理器配置

4. **样式问题**
   - 检查 CSS 样式是否正确加载
   - 检查组件插槽内容是否正确
