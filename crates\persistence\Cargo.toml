[package]
name = "moduforge-persistence"
version = {workspace=true}
edition = {workspace=true}
description = "moduforge 持久化"
authors = {workspace=true}
license = {workspace=true}
documentation = {workspace=true}
homepage = {workspace=true}
repository = {workspace=true}
[lib]
name = "mf_persistence"
path = "src/lib.rs"
[dependencies]
anyhow = { workspace = true }
async-trait = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
tracing = { workspace = true }
tokio = { workspace = true, features = ["rt-multi-thread", "macros", "sync", "time"] }
parking_lot = { workspace = true }
dashmap = { workspace = true }
uuid = { workspace = true, features = ["v4"] }
zstd = "0.13"
crc32fast = "1.4"
time = { version = "0.3", features = ["formatting"] }
rusqlite = { version = "0.37.0", features = ["bundled", "chrono", "serde_json"] }
tokio-util = { version = "0.7", features = ["rt"] }
chrono = { workspace = true }

# core/state for integration types
moduforge-state = { path = "../state", version = "0.4.12" }
moduforge-model = { path = "../model", version = "0.4.12" }
moduforge-transform = { path = "../transform", version = "0.4.12" }
moduforge-core = { path = "../core", version = "0.4.12" }
